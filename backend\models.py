#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

import sqlite3
import os
from datetime import datetime

class Database:
    def __init__(self, db_path='data/components.db'):
        # 确保使用绝对路径
        if not os.path.isabs(db_path):
            # 获取项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            self.db_path = os.path.join(project_root, db_path)
        else:
            self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 创建制造商表（1级分类）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS manufacturers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建产品组表（2级分类）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS product_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                manufacturer_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (manufacturer_id) REFERENCES manufacturers (id),
                UNIQUE(manufacturer_id, name)
            )
        ''')
        
        # 创建子产品组表（3级分类）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sub_product_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_group_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_group_id) REFERENCES product_groups (id),
                UNIQUE(product_group_id, name)
            )
        ''')
        
        # 创建元器件表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS components (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                manufacturer_id INTEGER NOT NULL,
                product_group_id INTEGER NOT NULL,
                sub_product_group_id INTEGER NOT NULL,
                model TEXT NOT NULL,
                description TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (manufacturer_id) REFERENCES manufacturers (id),
                FOREIGN KEY (product_group_id) REFERENCES product_groups (id),
                FOREIGN KEY (sub_product_group_id) REFERENCES sub_product_groups (id)
            )
        ''')
        
        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_components_model ON components (model)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_components_description ON components (description)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_components_manufacturer ON components (manufacturer_id)')
        
        conn.commit()
        conn.close()
        
        # 插入一些初始数据
        self.insert_initial_data()
    
    def clear_all_data(self):
        """清空所有数据"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 删除所有数据（按依赖关系顺序）
        cursor.execute('DELETE FROM components')
        cursor.execute('DELETE FROM sub_product_groups')
        cursor.execute('DELETE FROM product_groups')
        cursor.execute('DELETE FROM manufacturers')

        conn.commit()
        conn.close()

    def insert_initial_data(self):
        """插入真实的分类数据 - 完全层级结构"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 检查是否已有数据
        cursor.execute('SELECT COUNT(*) FROM manufacturers')
        if cursor.fetchone()[0] > 0:
            conn.close()
            return

        # 1. 插入制造商数据（1级分类）
        manufacturers = [
            'AMS', 'AZTECH', 'AIRTAC', 'AUO', 'ATI', 'ATILQ', 'ATMEL', 'ATTERN', 'AUTONICS', 'BALLUFF',
            'BAUTECH', 'BAUMER', 'BAYMARS', 'BECKHOFF', 'CHINT', 'COGNEX', 'DANFOSS', 'DATALOGIC',
            'DELTA', 'DEPRAG', 'EATON', 'DOLD', 'EPSON', 'FISCHNER', 'EVN', 'FESTO', 'FINGER',
            'FRIESISCH', 'FURNESS', 'GESPA', 'GOOGOLTECH', 'GUOLANG', 'HANLASER', 'HARTING', 'HI',
            'HONEYWELL', 'IAI', 'IFM', 'INVANCE', 'KIMO', 'KARRILL', 'KAEU', 'KEYENCE', 'KPCO',
            'KUKA', 'LASCAUX', 'LEADSHINE', 'LENZE', 'MEANWELL', 'MENNEKES', 'MITSUBISHI', 'MOXA',
            'MPULE', 'NACHI', 'NEMICON', 'OMRON-LINK', 'OMCIS', 'OMOT', 'PANASONIC', 'PROFACE',
            'REXROTH', 'SCHNEIDER', 'SHINCO', 'SICK', 'SIEMENS', 'STAUBLI', 'SAVI', 'TENG',
            'TURICK', 'WAIN', 'WEINVIEW', 'WERTU', 'YUAN'
        ]

        for name in manufacturers:
            cursor.execute('INSERT INTO manufacturers (name, description) VALUES (?, ?)', (name, ''))

        # 2. 插入产品组数据（2级分类）- 每个制造商都有所有产品组
        product_groups = [
            '传感器', '气动', '显示器', '单片机', '电源', '马达驱动器', '控制器', '工业电器',
            '工业相机', '变频器', '扫码器', '螺丝机', '断路器', '继电器模块', '工业机器人',
            '连接器', '夹爪模块', '压差计', '运动控制', '激光器', '中继器模块', '电缸',
            '步进驱动器', '通讯模块', '编码器', '触摸屏', '液压'
        ]

        # 获取所有制造商ID
        cursor.execute('SELECT id FROM manufacturers')
        manufacturer_ids = [row[0] for row in cursor.fetchall()]

        # 为每个制造商插入所有产品组
        for mfg_id in manufacturer_ids:
            for pg_name in product_groups:
                cursor.execute('INSERT INTO product_groups (manufacturer_id, name, description) VALUES (?, ?, ?)',
                             (mfg_id, pg_name, ''))

        # 3. 插入子产品组数据（3级分类）- 每个产品组都有所有子产品组
        sub_product_groups = [
            '磁性传感器', '接近开关', '气缸', '液晶屏', '力传感器', '马达传感器', '工业机器人',
            '连接器', '夹爪模块', '压差计', '运动控制', '激光器', '中继器模块', '电缸',
            '步进驱动器', '通讯模块', '编码器', '触摸屏', '液压', '变频器', '扫码器',
            '螺丝机', '断路器', '继电器模块', '工业电器', '工业相机', '单片机', '电源',
            '马达驱动器', '控制器', '传感器'
        ]

        # 获取所有产品组ID
        cursor.execute('SELECT id FROM product_groups')
        product_group_ids = [row[0] for row in cursor.fetchall()]

        # 为每个产品组插入所有子产品组
        for pg_id in product_group_ids:
            for spg_name in sub_product_groups:
                cursor.execute('INSERT INTO sub_product_groups (product_group_id, name, description) VALUES (?, ?, ?)',
                             (pg_id, spg_name, ''))

        conn.commit()
        conn.close()

        # 插入示例元器件数据
        self.insert_sample_components()

    def insert_sample_components(self):
        """插入示例元器件数据"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # 检查是否已有元器件数据
        cursor.execute('SELECT COUNT(*) FROM components')
        if cursor.fetchone()[0] > 0:
            conn.close()
            return

        # 获取一些制造商、产品组、子产品组的ID用于示例数据
        cursor.execute('SELECT id FROM manufacturers LIMIT 10')
        manufacturer_ids = [row[0] for row in cursor.fetchall()]

        if not manufacturer_ids:
            conn.close()
            return

        # 示例元器件数据
        sample_components = [
            # 传感器类
            {'model': 'E2E-X5ME1', 'description': '接近开关，检测距离5mm，NPN输出', 'notes': '适用于金属检测'},
            {'model': 'E3Z-D61', 'description': '光电传感器，漫反射型，检测距离0.1m', 'notes': '红外LED光源'},
            {'model': 'E2A-M18KS08-WP-B1', 'description': '电感式接近开关，M18螺纹，8mm检测距离', 'notes': '防水等级IP67'},

            # 气动元件
            {'model': 'SMC-CY1S25H-300B', 'description': '标准气缸，缸径25mm，行程300mm', 'notes': '双作用气缸，带磁性开关槽'},
            {'model': 'FESTO-DSBC-32-80-PPVA-N3', 'description': '标准气缸，缸径32mm，行程80mm', 'notes': 'ISO 15552标准'},
            {'model': 'SMC-SY3120-5LZ-C4', 'description': '电磁阀，5/2通，单线圈', 'notes': '24VDC，插头式连接'},

            # 电源模块
            {'model': 'MEAN WELL LRS-350-24', 'description': '开关电源，24V/14.6A，350W', 'notes': '85-264VAC输入，效率89%'},
            {'model': 'PHOENIX CONTACT QUINT-PS-100-240AC/24DC/5', 'description': 'DIN导轨电源，24V/5A，120W', 'notes': '宽输入电压范围'},

            # 控制器
            {'model': 'SIEMENS 6ES7214-1AG40-0XB0', 'description': 'S7-1200 CPU 1214C，14DI/10DO/2AI', 'notes': '集成以太网接口'},
            {'model': 'OMRON CP1E-N30DT-D', 'description': 'CP1E系列PLC，18DI/12DO', 'notes': '内置RS232/RS485通讯'},

            # 变频器
            {'model': 'DELTA VFD015M43A', 'description': '变频器，1.5kW，三相380V', 'notes': '矢量控制，内置PID'},
            {'model': 'SCHNEIDER ATV12H075M2', 'description': '变频器，0.75kW，单相220V', 'notes': '紧凑型设计'},

            # 伺服系统
            {'model': 'PANASONIC MSMD012G1U', 'description': '伺服电机，100W，3000rpm', 'notes': '带增量编码器'},
            {'model': 'YASKAWA SGDV-2R8A01A', 'description': '伺服驱动器，200V，750W', 'notes': 'MECHATROLINK-III通讯'},

            # 工业相机
            {'model': 'BASLER acA1300-60gm', 'description': '工业相机，130万像素，60fps', 'notes': 'GigE接口，全局快门'},
            {'model': 'COGNEX In-Sight 7000', 'description': '智能相机，视觉处理器', 'notes': '内置图像处理算法'},

            # 连接器
            {'model': 'PHOENIX CONTACT MC 1,5/ 8-ST-3,5', 'description': '插拔式接线端子，8位，3.5mm间距', 'notes': '额定电流8A'},
            {'model': 'HARTING 09330006104', 'description': 'Han工业连接器，6+PE', 'notes': 'IP65防护等级'},

            # 断路器
            {'model': 'SCHNEIDER C65N-C10', 'description': '小型断路器，C型，10A，1P', 'notes': '6kA分断能力'},
            {'model': 'ABB S201-B16', 'description': '小型断路器，B型，16A，1P', 'notes': '6kA分断能力'},

            # 继电器
            {'model': 'OMRON MY2N-GS DC24', 'description': '中间继电器，2组转换触点，24VDC', 'notes': '8脚方形插座'},
            {'model': 'SCHNEIDER RXM2AB2P7', 'description': '中间继电器，2组转换触点，230VAC', 'notes': '插拔式设计'}
        ]

        # 为每个示例元器件随机分配分类
        for i, component in enumerate(sample_components):
            # 随机选择制造商
            manufacturer_id = manufacturer_ids[i % len(manufacturer_ids)]

            # 获取该制造商的产品组
            cursor.execute('SELECT id FROM product_groups WHERE manufacturer_id = ? LIMIT 5', (manufacturer_id,))
            product_group_ids = [row[0] for row in cursor.fetchall()]
            if not product_group_ids:
                continue

            product_group_id = product_group_ids[i % len(product_group_ids)]

            # 获取该产品组的子产品组
            cursor.execute('SELECT id FROM sub_product_groups WHERE product_group_id = ? LIMIT 5', (product_group_id,))
            sub_product_group_ids = [row[0] for row in cursor.fetchall()]
            if not sub_product_group_ids:
                continue

            sub_product_group_id = sub_product_group_ids[i % len(sub_product_group_ids)]

            # 插入元器件数据
            cursor.execute('''
                INSERT INTO components (manufacturer_id, product_group_id, sub_product_group_id, model, description, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (manufacturer_id, product_group_id, sub_product_group_id,
                  component['model'], component['description'], component['notes']))

        conn.commit()
        conn.close()

    def reinitialize_data(self):
        """重新初始化数据库数据"""
        self.clear_all_data()
        self.insert_initial_data()

# 全局数据库实例
db = Database()
