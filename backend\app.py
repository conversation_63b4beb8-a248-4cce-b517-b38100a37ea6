#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用主文件
"""

from flask import Flask, jsonify, request, send_from_directory, send_file
from flask_cors import CORS
import os
import json
import sqlite3
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
import openpyxl.utils
from models import db

def create_app():
    app = Flask(__name__, static_folder='../frontend', static_url_path='')
    CORS(app)  # 允许跨域请求
    
    # 主页路由
    @app.route('/')
    def index():
        return send_from_directory('../frontend', 'index.html')
    
    # 获取所有制造商
    @app.route('/api/manufacturers', methods=['GET'])
    def get_manufacturers():
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM manufacturers ORDER BY name')
        manufacturers = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(manufacturers)
    
    # 根据制造商ID获取产品组
    @app.route('/api/manufacturers/<int:manufacturer_id>/product_groups', methods=['GET'])
    def get_product_groups(manufacturer_id):
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM product_groups WHERE manufacturer_id = ? ORDER BY name', (manufacturer_id,))
        product_groups = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(product_groups)
    
    # 根据产品组ID获取子产品组
    @app.route('/api/product_groups/<int:product_group_id>/sub_product_groups', methods=['GET'])
    def get_sub_product_groups(product_group_id):
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM sub_product_groups WHERE product_group_id = ? ORDER BY name', (product_group_id,))
        sub_product_groups = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify(sub_product_groups)

    # 获取完整的分类树结构（优化版本）
    @app.route('/api/category_tree', methods=['GET'])
    def get_category_tree():
        conn = db.get_connection()
        cursor = conn.cursor()

        # 一次性获取所有数据
        cursor.execute('''
            SELECT
                m.id as manufacturer_id, m.name as manufacturer_name,
                pg.id as product_group_id, pg.name as product_group_name,
                spg.id as sub_product_group_id, spg.name as sub_product_group_name,
                COUNT(c.id) as component_count
            FROM manufacturers m
            LEFT JOIN product_groups pg ON m.id = pg.manufacturer_id
            LEFT JOIN sub_product_groups spg ON pg.id = spg.product_group_id
            LEFT JOIN components c ON spg.id = c.sub_product_group_id
            GROUP BY m.id, m.name, pg.id, pg.name, spg.id, spg.name
            ORDER BY m.name, pg.name, spg.name
        ''')

        results = cursor.fetchall()
        conn.close()

        # 构建树形结构
        tree = []
        manufacturers = {}

        for row in results:
            manufacturer_id = row['manufacturer_id']
            manufacturer_name = row['manufacturer_name']
            product_group_id = row['product_group_id']
            product_group_name = row['product_group_name']
            sub_product_group_id = row['sub_product_group_id']
            sub_product_group_name = row['sub_product_group_name']
            component_count = row['component_count']

            # 创建制造商节点
            if manufacturer_id not in manufacturers:
                manufacturer_node = {
                    'id': f'm_{manufacturer_id}',
                    'name': manufacturer_name,
                    'type': 'manufacturer',
                    'children': []
                }
                manufacturers[manufacturer_id] = manufacturer_node
                tree.append(manufacturer_node)

            manufacturer_node = manufacturers[manufacturer_id]

            # 如果没有产品组，跳过
            if not product_group_id:
                continue

            # 查找或创建产品组节点
            product_group_node = None
            for child in manufacturer_node['children']:
                if child['id'] == f'pg_{product_group_id}':
                    product_group_node = child
                    break

            if not product_group_node:
                product_group_node = {
                    'id': f'pg_{product_group_id}',
                    'name': product_group_name,
                    'type': 'product_group',
                    'children': []
                }
                manufacturer_node['children'].append(product_group_node)

            # 如果没有子产品组，跳过
            if not sub_product_group_id:
                continue

            # 创建子产品组节点
            sub_product_group_node = {
                'id': f'spg_{sub_product_group_id}',
                'name': sub_product_group_name,
                'type': 'sub_product_group',
                'sub_product_group_id': sub_product_group_id,
                'componentCount': component_count
            }
            product_group_node['children'].append(sub_product_group_node)

        return jsonify(tree)
    
    # 添加新制造商
    @app.route('/api/manufacturers', methods=['POST'])
    def add_manufacturer():
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        if not name:
            return jsonify({'error': '制造商名称不能为空'}), 400
        
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('INSERT INTO manufacturers (name, description) VALUES (?, ?)', (name, description))
            conn.commit()
            manufacturer_id = cursor.lastrowid
            conn.close()
            return jsonify({'id': manufacturer_id, 'name': name, 'description': description})
        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'error': '制造商名称已存在'}), 400
    
    # 添加新产品组
    @app.route('/api/product_groups', methods=['POST'])
    def add_product_group():
        data = request.get_json()
        manufacturer_id = data.get('manufacturer_id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        if not manufacturer_id or not name:
            return jsonify({'error': '制造商ID和产品组名称不能为空'}), 400
        
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('INSERT INTO product_groups (manufacturer_id, name, description) VALUES (?, ?, ?)', 
                         (manufacturer_id, name, description))
            conn.commit()
            product_group_id = cursor.lastrowid
            conn.close()
            return jsonify({'id': product_group_id, 'manufacturer_id': manufacturer_id, 'name': name, 'description': description})
        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'error': '该制造商下已存在相同名称的产品组'}), 400
    
    # 添加新子产品组
    @app.route('/api/sub_product_groups', methods=['POST'])
    def add_sub_product_group():
        data = request.get_json()
        product_group_id = data.get('product_group_id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        if not product_group_id or not name:
            return jsonify({'error': '产品组ID和子产品组名称不能为空'}), 400
        
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('INSERT INTO sub_product_groups (product_group_id, name, description) VALUES (?, ?, ?)', 
                         (product_group_id, name, description))
            conn.commit()
            sub_product_group_id = cursor.lastrowid
            conn.close()
            return jsonify({'id': sub_product_group_id, 'product_group_id': product_group_id, 'name': name, 'description': description})
        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'error': '该产品组下已存在相同名称的子产品组'}), 400
    
    # 添加元器件
    @app.route('/api/components', methods=['POST'])
    def add_component():
        data = request.get_json()
        manufacturer_id = data.get('manufacturer_id')
        product_group_id = data.get('product_group_id')
        sub_product_group_id = data.get('sub_product_group_id')
        model = data.get('model', '').strip()
        description = data.get('description', '').strip()
        notes = data.get('notes', '').strip()

        if not all([manufacturer_id, product_group_id, sub_product_group_id, model]):
            return jsonify({'error': '所有分类和型号不能为空'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO components (manufacturer_id, product_group_id, sub_product_group_id, model, description, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (manufacturer_id, product_group_id, sub_product_group_id, model, description, notes))
        conn.commit()
        component_id = cursor.lastrowid
        conn.close()

        return jsonify({
            'id': component_id,
            'manufacturer_id': manufacturer_id,
            'product_group_id': product_group_id,
            'sub_product_group_id': sub_product_group_id,
            'model': model,
            'description': description,
            'notes': notes
        })

    # 获取所有元器件（用于测试和调试）
    @app.route('/api/components', methods=['GET'])
    def get_all_components():
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT c.*, m.name as manufacturer_name, pg.name as product_group_name, spg.name as sub_product_group_name
            FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            ORDER BY c.created_at DESC
            LIMIT 100
        ''')

        components = []
        for row in cursor.fetchall():
            components.append({
                'id': row['id'],
                'manufacturer_id': row['manufacturer_id'],
                'manufacturer_name': row['manufacturer_name'],
                'product_group_id': row['product_group_id'],
                'product_group_name': row['product_group_name'],
                'sub_product_group_id': row['sub_product_group_id'],
                'sub_product_group_name': row['sub_product_group_name'],
                'model': row['model'],
                'description': row['description'],
                'notes': row['notes'],
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            })

        conn.close()
        return jsonify({
            'components': components,
            'total': len(components)
        })

    # 批量添加元器件 - 数据验证
    @app.route('/api/components/batch/validate', methods=['POST'])
    def validate_batch_components():
        data = request.get_json()
        components = data.get('components', [])

        if not components:
            return jsonify({'error': '元器件列表不能为空'}), 400

        validation_results = []
        valid_count = 0

        for i, component in enumerate(components):
            result = {
                'index': i,
                'valid': True,
                'errors': [],
                'data': component
            }

            # 验证必填字段
            manufacturer_id = component.get('manufacturer_id')
            product_group_id = component.get('product_group_id')
            sub_product_group_id = component.get('sub_product_group_id')
            model = component.get('model', '').strip()

            if not manufacturer_id:
                result['errors'].append('制造商不能为空')
                result['valid'] = False
            if not product_group_id:
                result['errors'].append('产品组不能为空')
                result['valid'] = False
            if not sub_product_group_id:
                result['errors'].append('子产品组不能为空')
                result['valid'] = False
            if not model:
                result['errors'].append('型号不能为空')
                result['valid'] = False

            # 验证型号长度和特殊字符
            if model and len(model) > 100:
                result['errors'].append('型号长度不能超过100个字符')
                result['valid'] = False

            # 验证描述和备注长度
            description = component.get('description', '').strip()
            notes = component.get('notes', '').strip()

            if description and len(description) > 500:
                result['errors'].append('描述长度不能超过500个字符')
                result['valid'] = False

            if notes and len(notes) > 1000:
                result['errors'].append('备注长度不能超过1000个字符')
                result['valid'] = False

            if result['valid']:
                valid_count += 1

            validation_results.append(result)

        return jsonify({
            'total': len(components),
            'valid': valid_count,
            'invalid': len(components) - valid_count,
            'results': validation_results
        })

    # 批量添加元器件
    @app.route('/api/components/batch', methods=['POST'])
    def add_components_batch():
        data = request.get_json()
        components = data.get('components', [])

        if not components:
            return jsonify({'error': '元器件列表不能为空'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()
        added_components = []
        failed_components = []

        try:
            # 开始事务
            cursor.execute('BEGIN TRANSACTION')

            for i, component in enumerate(components):
                try:
                    manufacturer_id = component.get('manufacturer_id')
                    product_group_id = component.get('product_group_id')
                    sub_product_group_id = component.get('sub_product_group_id')
                    model = component.get('model', '').strip()
                    description = component.get('description', '').strip()
                    notes = component.get('notes', '').strip()

                    # 验证必填字段
                    if not all([manufacturer_id, product_group_id, sub_product_group_id, model]):
                        failed_components.append({
                            'index': i,
                            'model': model,
                            'error': '必填字段不完整'
                        })
                        continue

                    # 验证外键是否存在
                    cursor.execute('SELECT id FROM manufacturers WHERE id = ?', (manufacturer_id,))
                    if not cursor.fetchone():
                        failed_components.append({
                            'index': i,
                            'model': model,
                            'error': '制造商不存在'
                        })
                        continue

                    cursor.execute('SELECT id FROM product_groups WHERE id = ? AND manufacturer_id = ?',
                                 (product_group_id, manufacturer_id))
                    if not cursor.fetchone():
                        failed_components.append({
                            'index': i,
                            'model': model,
                            'error': '产品组不存在或不属于所选制造商'
                        })
                        continue

                    cursor.execute('SELECT id FROM sub_product_groups WHERE id = ? AND product_group_id = ?',
                                 (sub_product_group_id, product_group_id))
                    if not cursor.fetchone():
                        failed_components.append({
                            'index': i,
                            'model': model,
                            'error': '子产品组不存在或不属于所选产品组'
                        })
                        continue

                    # 插入数据
                    cursor.execute('''
                        INSERT INTO components (manufacturer_id, product_group_id, sub_product_group_id, model, description, notes)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (manufacturer_id, product_group_id, sub_product_group_id, model, description, notes))

                    added_components.append({
                        'index': i,
                        'id': cursor.lastrowid,
                        'model': model,
                        'description': description,
                        'notes': notes
                    })

                except Exception as e:
                    failed_components.append({
                        'index': i,
                        'model': component.get('model', ''),
                        'error': str(e)
                    })

            # 如果有失败的记录，回滚事务
            if failed_components:
                cursor.execute('ROLLBACK')
                conn.close()
                return jsonify({
                    'success': False,
                    'error': '部分数据验证失败，未保存任何数据',
                    'added': 0,
                    'failed': len(failed_components),
                    'failed_components': failed_components
                }), 400

            # 提交事务
            cursor.execute('COMMIT')
            conn.close()

            return jsonify({
                'success': True,
                'added': len(added_components),
                'failed': 0,
                'components': added_components
            })

        except Exception as e:
            cursor.execute('ROLLBACK')
            conn.close()
            return jsonify({
                'success': False,
                'error': f'批量添加失败: {str(e)}',
                'added': 0,
                'failed': len(components)
            }), 500

    # 搜索元器件
    @app.route('/api/components/search', methods=['GET'])
    def search_components():
        keyword = request.args.get('keyword', '').strip()
        manufacturer_id = request.args.get('manufacturer_id')
        product_group_id = request.args.get('product_group_id')
        sub_product_group_id = request.args.get('sub_product_group_id')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))

        conn = db.get_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_clause = 'WHERE 1=1'
        params = []

        if keyword:
            where_clause += ' AND (c.model LIKE ? OR c.description LIKE ? OR c.notes LIKE ?)'
            keyword_param = f'%{keyword}%'
            params.extend([keyword_param, keyword_param, keyword_param])

        if manufacturer_id:
            where_clause += ' AND c.manufacturer_id = ?'
            params.append(manufacturer_id)

        if product_group_id:
            where_clause += ' AND c.product_group_id = ?'
            params.append(product_group_id)

        if sub_product_group_id:
            where_clause += ' AND c.sub_product_group_id = ?'
            params.append(sub_product_group_id)

        # 获取总数
        count_sql = f'''
            SELECT COUNT(*) FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            {where_clause}
        '''
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f'''
            SELECT c.*, m.name as manufacturer_name, pg.name as product_group_name, spg.name as sub_product_group_name
            FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            {where_clause}
            ORDER BY m.name, pg.name, spg.name, c.model
            LIMIT ? OFFSET ?
        '''
        cursor.execute(data_sql, params + [page_size, offset])
        components = []
        for row in cursor.fetchall():
            component = dict(row)
            # 添加分类路径
            component['category_path'] = f"{component['manufacturer_name']} > {component['product_group_name']} > {component['sub_product_group_name']}"
            components.append(component)
        conn.close()

        return jsonify({
            'data': components,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    # 根据分类获取元器件
    @app.route('/api/components/by_category', methods=['GET'])
    def get_components_by_category():
        manufacturer_id = request.args.get('manufacturer_id')
        product_group_id = request.args.get('product_group_id')
        sub_product_group_id = request.args.get('sub_product_group_id')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))

        if not sub_product_group_id:
            return jsonify({'error': '必须指定子产品组ID'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # 获取总数
        count_sql = '''
            SELECT COUNT(*) FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            WHERE c.sub_product_group_id = ?
        '''
        cursor.execute(count_sql, (sub_product_group_id,))
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = '''
            SELECT c.*, m.name as manufacturer_name, pg.name as product_group_name, spg.name as sub_product_group_name
            FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            WHERE c.sub_product_group_id = ?
            ORDER BY c.model
            LIMIT ? OFFSET ?
        '''
        cursor.execute(data_sql, (sub_product_group_id, page_size, offset))
        components = []
        for row in cursor.fetchall():
            component = dict(row)
            # 添加分类路径
            component['category_path'] = f"{component['manufacturer_name']} > {component['product_group_name']} > {component['sub_product_group_name']}"
            components.append(component)
        conn.close()

        return jsonify({
            'data': components,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    # 获取导出数据（JSON格式，供前端SheetJS使用）
    @app.route('/api/components/export-data', methods=['POST'])
    def get_export_data():
        data = request.get_json()
        component_ids = data.get('component_ids', [])

        if not component_ids:
            return jsonify({'error': '没有选择要导出的元器件'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # 获取选中的元器件数据
        placeholders = ','.join(['?' for _ in component_ids])
        cursor.execute(f'''
            SELECT c.*, m.name as manufacturer_name, pg.name as product_group_name, spg.name as sub_product_group_name
            FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            WHERE c.id IN ({placeholders})
            ORDER BY m.name, pg.name, spg.name, c.model
        ''', component_ids)

        components = cursor.fetchall()
        conn.close()

        if not components:
            return jsonify({'error': '没有找到要导出的元器件数据'}), 404

        # 转换为前端需要的格式
        export_data = []
        for component in components:
            export_data.append({
                'manufacturer_name': component['manufacturer_name'] or '',
                'product_group_name': component['product_group_name'] or '',
                'sub_product_group_name': component['sub_product_group_name'] or '',
                'model': component['model'] or '',
                'description': component['description'] or '',
                'notes': component['notes'] or ''
            })

        return jsonify({'data': export_data})

    # 导出元器件数据到Excel（保留原有接口作为备用）
    @app.route('/api/components/export', methods=['POST'])
    def export_components():
        data = request.get_json()
        component_ids = data.get('component_ids', [])

        if not component_ids:
            return jsonify({'error': '没有选择要导出的元器件'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # 获取选中的元器件数据
        placeholders = ','.join(['?' for _ in component_ids])
        cursor.execute(f'''
            SELECT c.*, m.name as manufacturer_name, pg.name as product_group_name, spg.name as sub_product_group_name
            FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            WHERE c.id IN ({placeholders})
            ORDER BY m.name, pg.name, spg.name, c.model
        ''', component_ids)

        components = cursor.fetchall()
        conn.close()

        if not components:
            return jsonify({'error': '没有找到要导出的元器件数据'}), 404

        # 创建Excel文件
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "元器件清单"

        # 设置表头
        headers = ['制造商(1级分类)', '产品组(2级分类)', '子产品(3级分类)', '型号', '描述', '备注']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 填充数据
        for row, component in enumerate(components, 2):
            ws.cell(row=row, column=1, value=component['manufacturer_name'])
            ws.cell(row=row, column=2, value=component['product_group_name'])
            ws.cell(row=row, column=3, value=component['sub_product_group_name'])
            ws.cell(row=row, column=4, value=component['model'])
            ws.cell(row=row, column=5, value=component['description'])
            ws.cell(row=row, column=6, value=component['notes'])

        # 调整列宽
        column_widths = [20, 20, 20, 25, 30, 30]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 保存文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'元器件清单_{timestamp}.xlsx'
        filepath = os.path.join('exports', filename)

        # 确保exports目录存在
        os.makedirs('exports', exist_ok=True)

        wb.save(filepath)

        # 返回文件，设置正确的响应头
        response = send_file(
            filepath,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    # ==================== 数据库管理API ====================

    # 数据库管理页面
    @app.route('/admin')
    def admin_page():
        return send_from_directory('../frontend', 'admin.html')

    # 验证管理员密码
    @app.route('/api/admin/auth', methods=['POST'])
    def admin_auth():
        data = request.get_json()
        password = data.get('password', '')

        # 固定密码：147258369
        if password == '147258369':
            return jsonify({'success': True, 'message': '验证成功'})
        else:
            return jsonify({'success': False, 'message': '密码错误'}), 401

    # 获取制造商列表（分页）
    @app.route('/api/admin/manufacturers', methods=['GET'])
    def admin_get_manufacturers():
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        search = request.args.get('search', '').strip()

        conn = db.get_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_clause = ""
        params = []
        if search:
            where_clause = "WHERE name LIKE ? OR description LIKE ?"
            search_param = f'%{search}%'
            params = [search_param, search_param]

        # 获取总数
        count_sql = f"SELECT COUNT(*) FROM manufacturers {where_clause}"
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f"SELECT * FROM manufacturers {where_clause} ORDER BY name LIMIT ? OFFSET ?"
        cursor.execute(data_sql, params + [page_size, offset])
        manufacturers = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'data': manufacturers,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    # 更新制造商
    @app.route('/api/admin/manufacturers/<int:manufacturer_id>', methods=['PUT'])
    def admin_update_manufacturer(manufacturer_id):
        data = request.get_json()
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not name:
            return jsonify({'error': '制造商名称不能为空'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('UPDATE manufacturers SET name = ?, description = ? WHERE id = ?',
                         (name, description, manufacturer_id))
            if cursor.rowcount == 0:
                conn.close()
                return jsonify({'error': '制造商不存在'}), 404
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': '更新成功'})
        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'error': '制造商名称已存在'}), 400

    # 删除制造商
    @app.route('/api/admin/manufacturers/<int:manufacturer_id>', methods=['DELETE'])
    def admin_delete_manufacturer(manufacturer_id):
        conn = db.get_connection()
        cursor = conn.cursor()

        # 检查是否有关联的产品组
        cursor.execute('SELECT COUNT(*) FROM product_groups WHERE manufacturer_id = ?', (manufacturer_id,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': '该制造商下还有产品组，无法删除'}), 400

        cursor.execute('DELETE FROM manufacturers WHERE id = ?', (manufacturer_id,))
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': '制造商不存在'}), 404

        conn.commit()
        conn.close()
        return jsonify({'success': True, 'message': '删除成功'})

    # 获取产品组列表（分页）
    @app.route('/api/admin/product_groups', methods=['GET'])
    def admin_get_product_groups():
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        search = request.args.get('search', '').strip()
        manufacturer_id = request.args.get('manufacturer_id')

        conn = db.get_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_clause = "WHERE 1=1"
        params = []

        if manufacturer_id:
            where_clause += " AND pg.manufacturer_id = ?"
            params.append(manufacturer_id)

        if search:
            where_clause += " AND (pg.name LIKE ? OR pg.description LIKE ? OR m.name LIKE ?)"
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param])

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) FROM product_groups pg
            JOIN manufacturers m ON pg.manufacturer_id = m.id
            {where_clause}
        """
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT pg.*, m.name as manufacturer_name
            FROM product_groups pg
            JOIN manufacturers m ON pg.manufacturer_id = m.id
            {where_clause}
            ORDER BY m.name, pg.name
            LIMIT ? OFFSET ?
        """
        cursor.execute(data_sql, params + [page_size, offset])
        product_groups = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'data': product_groups,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    # 更新产品组
    @app.route('/api/admin/product_groups/<int:product_group_id>', methods=['PUT'])
    def admin_update_product_group(product_group_id):
        data = request.get_json()
        manufacturer_id = data.get('manufacturer_id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not manufacturer_id or not name:
            return jsonify({'error': '制造商ID和产品组名称不能为空'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('UPDATE product_groups SET manufacturer_id = ?, name = ?, description = ? WHERE id = ?',
                         (manufacturer_id, name, description, product_group_id))
            if cursor.rowcount == 0:
                conn.close()
                return jsonify({'error': '产品组不存在'}), 404
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': '更新成功'})
        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'error': '该制造商下已存在相同名称的产品组'}), 400

    # 删除产品组
    @app.route('/api/admin/product_groups/<int:product_group_id>', methods=['DELETE'])
    def admin_delete_product_group(product_group_id):
        conn = db.get_connection()
        cursor = conn.cursor()

        # 检查是否有关联的子产品组
        cursor.execute('SELECT COUNT(*) FROM sub_product_groups WHERE product_group_id = ?', (product_group_id,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': '该产品组下还有子产品组，无法删除'}), 400

        cursor.execute('DELETE FROM product_groups WHERE id = ?', (product_group_id,))
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': '产品组不存在'}), 404

        conn.commit()
        conn.close()
        return jsonify({'success': True, 'message': '删除成功'})

    # 获取子产品组列表（分页）
    @app.route('/api/admin/sub_product_groups', methods=['GET'])
    def admin_get_sub_product_groups():
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        search = request.args.get('search', '').strip()
        product_group_id = request.args.get('product_group_id')
        manufacturer_id = request.args.get('manufacturer_id')

        conn = db.get_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_clause = "WHERE 1=1"
        params = []

        if manufacturer_id:
            where_clause += " AND pg.manufacturer_id = ?"
            params.append(manufacturer_id)

        if product_group_id:
            where_clause += " AND spg.product_group_id = ?"
            params.append(product_group_id)

        if search:
            where_clause += " AND (spg.name LIKE ? OR spg.description LIKE ? OR pg.name LIKE ? OR m.name LIKE ?)"
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param, search_param])

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) FROM sub_product_groups spg
            JOIN product_groups pg ON spg.product_group_id = pg.id
            JOIN manufacturers m ON pg.manufacturer_id = m.id
            {where_clause}
        """
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT spg.*, pg.name as product_group_name, m.name as manufacturer_name, pg.manufacturer_id
            FROM sub_product_groups spg
            JOIN product_groups pg ON spg.product_group_id = pg.id
            JOIN manufacturers m ON pg.manufacturer_id = m.id
            {where_clause}
            ORDER BY m.name, pg.name, spg.name
            LIMIT ? OFFSET ?
        """
        cursor.execute(data_sql, params + [page_size, offset])
        sub_product_groups = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'data': sub_product_groups,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    # 更新子产品组
    @app.route('/api/admin/sub_product_groups/<int:sub_product_group_id>', methods=['PUT'])
    def admin_update_sub_product_group(sub_product_group_id):
        data = request.get_json()
        product_group_id = data.get('product_group_id')
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()

        if not product_group_id or not name:
            return jsonify({'error': '产品组ID和子产品组名称不能为空'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('UPDATE sub_product_groups SET product_group_id = ?, name = ?, description = ? WHERE id = ?',
                         (product_group_id, name, description, sub_product_group_id))
            if cursor.rowcount == 0:
                conn.close()
                return jsonify({'error': '子产品组不存在'}), 404
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': '更新成功'})
        except sqlite3.IntegrityError:
            conn.close()
            return jsonify({'error': '该产品组下已存在相同名称的子产品组'}), 400

    # 删除子产品组
    @app.route('/api/admin/sub_product_groups/<int:sub_product_group_id>', methods=['DELETE'])
    def admin_delete_sub_product_group(sub_product_group_id):
        conn = db.get_connection()
        cursor = conn.cursor()

        # 检查是否有关联的元器件
        cursor.execute('SELECT COUNT(*) FROM components WHERE sub_product_group_id = ?', (sub_product_group_id,))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': '该子产品组下还有元器件，无法删除'}), 400

        cursor.execute('DELETE FROM sub_product_groups WHERE id = ?', (sub_product_group_id,))
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': '子产品组不存在'}), 404

        conn.commit()
        conn.close()
        return jsonify({'success': True, 'message': '删除成功'})

    # 获取元器件列表（分页）
    @app.route('/api/admin/components', methods=['GET'])
    def admin_get_components():
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 10))
        search = request.args.get('search', '').strip()
        manufacturer_id = request.args.get('manufacturer_id')
        product_group_id = request.args.get('product_group_id')
        sub_product_group_id = request.args.get('sub_product_group_id')

        conn = db.get_connection()
        cursor = conn.cursor()

        # 构建查询条件
        where_clause = "WHERE 1=1"
        params = []

        if manufacturer_id:
            where_clause += " AND c.manufacturer_id = ?"
            params.append(manufacturer_id)

        if product_group_id:
            where_clause += " AND c.product_group_id = ?"
            params.append(product_group_id)

        if sub_product_group_id:
            where_clause += " AND c.sub_product_group_id = ?"
            params.append(sub_product_group_id)

        if search:
            where_clause += " AND (c.model LIKE ? OR c.description LIKE ? OR c.notes LIKE ? OR m.name LIKE ?)"
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param, search_param])

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            {where_clause}
        """
        cursor.execute(count_sql, params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT c.*, m.name as manufacturer_name, pg.name as product_group_name, spg.name as sub_product_group_name
            FROM components c
            JOIN manufacturers m ON c.manufacturer_id = m.id
            JOIN product_groups pg ON c.product_group_id = pg.id
            JOIN sub_product_groups spg ON c.sub_product_group_id = spg.id
            {where_clause}
            ORDER BY m.name, pg.name, spg.name, c.model
            LIMIT ? OFFSET ?
        """
        cursor.execute(data_sql, params + [page_size, offset])
        components = [dict(row) for row in cursor.fetchall()]

        conn.close()

        return jsonify({
            'data': components,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        })

    # 更新元器件
    @app.route('/api/admin/components/<int:component_id>', methods=['PUT'])
    def admin_update_component(component_id):
        data = request.get_json()
        manufacturer_id = data.get('manufacturer_id')
        product_group_id = data.get('product_group_id')
        sub_product_group_id = data.get('sub_product_group_id')
        model = data.get('model', '').strip()
        description = data.get('description', '').strip()
        notes = data.get('notes', '').strip()

        if not all([manufacturer_id, product_group_id, sub_product_group_id, model]):
            return jsonify({'error': '所有分类和型号不能为空'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE components
            SET manufacturer_id = ?, product_group_id = ?, sub_product_group_id = ?,
                model = ?, description = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (manufacturer_id, product_group_id, sub_product_group_id, model, description, notes, component_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': '元器件不存在'}), 404

        conn.commit()
        conn.close()
        return jsonify({'success': True, 'message': '更新成功'})

    # 删除元器件
    @app.route('/api/admin/components/<int:component_id>', methods=['DELETE'])
    def admin_delete_component(component_id):
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('DELETE FROM components WHERE id = ?', (component_id,))
        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': '元器件不存在'}), 404

        conn.commit()
        conn.close()
        return jsonify({'success': True, 'message': '删除成功'})

    return app
